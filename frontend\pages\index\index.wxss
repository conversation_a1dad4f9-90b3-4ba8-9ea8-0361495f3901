/* index.wxss */

/* Header Styles */
.header {
  background: linear-gradient(135deg, #0A4A39 0%, #0d5a47 100%);
  padding: 20rpx 30rpx 30rpx;
  color: white;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-section {
  display: flex;
  flex-direction: column;
}

.app-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.app-subtitle {
  font-size: 24rpx;
  opacity: 0.8;
}

.header-actions {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10rpx);
}

.action-icon {
  font-size: 24rpx;
}

/* Search Container */
.search-container {
  background: white;
  padding: 0 30rpx;
  max-height: 0;
  overflow: hidden;
  transition: all 0.3s ease;
  border-bottom: 1rpx solid #f0f0f0;
}

.search-container.show {
  max-height: 120rpx;
  padding: 20rpx 30rpx;
}

.search-bar {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.search-input-wrapper {
  flex: 1;
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 25rpx;
  padding: 0 20rpx;
  height: 70rpx;
}

.search-prefix-icon {
  font-size: 20rpx;
  color: #999;
  margin-right: 15rpx;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  height: 100%;
}

.search-clear {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background: #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 10rpx;
}

.clear-icon {
  font-size: 24rpx;
  color: white;
}

.search-action {
  background: #0A4A39;
  color: white;
  padding: 0 30rpx;
  height: 70rpx;
  border-radius: 25rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-action-text {
  font-size: 28rpx;
}



/* Tab Navigation */
.tab-nav {
  display: flex;
  background-color: var(--primary-color);
  color: var(--text-on-primary);
  position: sticky;
  top: 0;
  z-index: 100;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 24rpx 0;
  font-size: 28rpx;
  position: relative;
}

.tab-item.active:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: white;
  border-radius: 2rpx;
}

/* Filter Bar */
.filter-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background-color: var(--card-background);
  border-bottom: 1rpx solid var(--border-color);
}

.filter-title {
  font-size: 32rpx;
  font-weight: 500;
}

.filter-btn {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: var(--text-secondary);
}

.filter-icon {
  font-size: 32rpx;
  margin-left: 8rpx;
}

/* Filter Panel */
.filter-panel {
  position: fixed;
  top: 0;
  right: -100%;
  width: 70%;
  height: 100%;
  background-color: var(--card-background);
  z-index: 1000;
  box-shadow: -4rpx 0 10rpx rgba(0, 0, 0, 0.1);
  transition: right 0.3s ease;
  padding: 30rpx 20rpx;
  box-sizing: border-box;
}

.filter-panel.show {
  right: 0;
}

.filter-section {
  margin-bottom: 30rpx;
}

.filter-section-title {
  font-size: 28rpx;
  font-weight: 500;
  margin-bottom: 16rpx;
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
}

.filter-option {
  padding: 12rpx 20rpx;
  border: 1rpx solid var(--border-color);
  border-radius: 4rpx;
  margin-right: 16rpx;
  margin-bottom: 16rpx;
  font-size: 24rpx;
}

.filter-option.active {
  background-color: var(--primary-color);
  color: var(--text-on-primary);
  border-color: var(--primary-color);
}

.filter-input {
  width: 100%;
  height: 70rpx;
  border: 1rpx solid var(--border-color);
  border-radius: 4rpx;
  padding: 0 16rpx;
  box-sizing: border-box;
  font-size: 26rpx;
}

.filter-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 40rpx;
}

.btn-reset, .btn-apply {
  width: 45%;
  height: 80rpx;
  border-radius: 4rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
}

.btn-reset {
  background-color: #f5f5f5;
  color: var(--text-secondary);
}

.btn-apply {
  background-color: var(--primary-color);
  color: var(--text-on-primary);
}

/* Match List */
.match-list {
  padding: 20rpx;
}

/* 日期分组样式 */
.date-group {
  margin-bottom: 40rpx;
}

.date-header {
  background-color: var(--background-color);
  padding: 16rpx 20rpx;
  margin-bottom: 16rpx;
  border-radius: 8rpx;
  border-left: 4rpx solid var(--primary-color);
}

.date-display {
  font-size: 28rpx;
  font-weight: 500;
  color: var(--primary-color);
}

/* 比赛卡片样式 */
.match-card {
  background: white;
  border-radius: 20rpx;
  margin: 20rpx 30rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
  padding: 30rpx;
}

.match-card:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.15);
}

/* 比赛头部信息 */
.match-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
  padding-bottom: 12rpx;
  border-bottom: 1rpx solid var(--border-color);
}

.match-type {
  font-size: 28rpx;
  font-weight: 500;
  color: var(--primary-color);
}

.match-status {
  display: flex;
  align-items: center;
}

.status-completed {
  background-color: #4caf50;
  color: white;
}

.status-ongoing {
  background-color: #0A4A39;
  color: white;
}

.status-registration {
  background-color: #2e7d32;
  color: white;
}

/* 比赛信息 */
.match-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
  font-size: 24rpx;
  color: var(--text-secondary);
}

.venue-info {
  flex: 1;
}

.time-info {
  font-weight: 500;
}

/* 选手区域 */
.players-section {
  margin-bottom: 20rpx;
}

.team-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12rpx 0;
}

.team-players {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.player {
  display: flex;
  align-items: center;
  margin-bottom: 4rpx;
}

.player-name {
  font-size: 26rpx;
  font-weight: 500;
  margin-right: 8rpx;
}

.player-ranking {
  font-size: 22rpx;
  color: var(--text-tertiary);
}

.team-score {
  display: flex;
  gap: 8rpx;
}

.set-score {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: 500;
  background-color: #f5f5f5;
  border-radius: 6rpx;
  color: var(--text-secondary);
}

.set-score.winner {
  background-color: var(--primary-color);
  color: white;
}

/* VS 分隔符 */
.vs-divider {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8rpx 0;
  margin: 8rpx 0;
}

.vs-text {
  font-size: 20rpx;
  color: var(--text-tertiary);
  margin: 0 12rpx;
}

.winner-mark {
  font-size: 20rpx;
  color: var(--primary-color);
  font-weight: bold;
}

/* 操作按钮 */
.match-actions {
  display: flex;
  justify-content: center;
  padding-top: 16rpx;
  border-top: 1rpx solid var(--border-color);
}

.btn-register,
.btn-detail {
  padding: 12rpx 32rpx;
  border-radius: 6rpx;
  font-size: 26rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-register {
  background-color: var(--primary-color);
  color: white;
}

.btn-detail {
  background-color: #f8f9fa;
  color: var(--text-secondary);
  border: 1rpx solid var(--border-color);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: var(--primary-color);
  color: var(--text-on-primary);
  padding: 16rpx 20rpx;
  font-size: 26rpx;
}

.status-label {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
  background-color: rgba(255, 255, 255, 0.2);
  line-height: 1;
}

.card-content {
  padding: 20rpx;
  display: flex;
}

.match-data {
  flex: 1;
  padding-right: 20rpx;
  border-right: 1rpx solid var(--border-color);
}

.player-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.player-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.player-avatar {
  font-size: 40rpx;
  margin-right: 16rpx;
}

.player-details {
  flex: 1;
}

.player-name {
  font-size: 28rpx;
  font-weight: 500;
  margin-bottom: 4rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200rpx;
}

.player-ranking {
  font-size: 22rpx;
  color: var(--text-tertiary);
}

.score-container {
  display: flex;
}

.set-score {
  width: 50rpx;
  height: 50rpx;
  line-height: 50rpx;
  text-align: center;
  font-size: 26rpx;
  margin-left: 10rpx;
  background-color: #f5f5f5;
  border-radius: 4rpx;
}

.set-score.winner {
  background-color: var(--primary-color);
  color: var(--text-on-primary);
}

.entry-section {
  width: 200rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding-left: 20rpx;
}

.match-info {
  text-align: center;
}

.match-venue {
  font-size: 24rpx;
  color: var(--text-secondary);
  margin-bottom: 8rpx;
}

.match-duration {
  font-size: 24rpx;
  color: var(--text-tertiary);
}

.entry-action {
  margin-top: 20rpx;
}

.entry-action .btn-primary {
  width: 100%;
  height: 70rpx;
  font-size: 26rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
}

/* Empty State */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-icon {
  font-size: 160rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: var(--text-tertiary);
}

/* Loading State */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(0, 0, 0, 0.1);
  border-top-color: var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16rpx;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loading-text {
  font-size: 26rpx;
  color: var(--text-tertiary);
}

/* Load More */
.load-more {
  text-align: center;
  padding: 20rpx 0;
  font-size: 26rpx;
  color: var(--text-secondary);
}

/* Skeleton Loader Styles */
.skeleton-card {
  background-color: var(--card-background);
  border-radius: 8rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  padding: 20rpx;
  animation: pulse 1.5s infinite ease-in-out;
}

.skeleton-header {
  height: 40rpx;
  background-color: #e0e0e0;
  border-radius: 4rpx;
  margin-bottom: 20rpx;
  width: 70%;
}

.skeleton-content {
  display: flex;
  justify-content: space-between;
}

.skeleton-row {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.skeleton-avatar {
  width: 50rpx;
  height: 50rpx;
  background-color: #e0e0e0;
  border-radius: 50%;
  margin-right: 16rpx;
}

.skeleton-text-group {
  flex: 1;
}

.skeleton-text {
  height: 24rpx;
  background-color: #e0e0e0;
  border-radius: 4rpx;
  margin-bottom: 8rpx;
}

.skeleton-text.short {
  width: 60%;
}

.skeleton-text.long {
  width: 90%;
}

.skeleton-right-section {
  width: 150rpx;
  height: 100rpx;
  background-color: #e0e0e0;
  border-radius: 4rpx;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

/* 新的比赛卡片样式 */
.match-title-section {
  flex: 1;
}

.match-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #1a1a1a;
  margin-bottom: 8rpx;
  line-height: 1.4;
}

.match-stage {
  font-size: 24rpx;
  color: #666;
  background: #f8f9fa;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  display: inline-block;
}

.status-badge {
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  min-width: 120rpx;
  justify-content: center;
}

.status-dot-animated {
  width: 8rpx;
  height: 8rpx;
  border-radius: 50%;
  background: #4caf50;
  margin-right: 8rpx;
  animation: pulse-dot 1.5s infinite;
}

@keyframes pulse-dot {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.6; transform: scale(1.3); }
}

.status-text {
  font-weight: 600;
}

/* 比赛元信息 */
.match-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-bottom: 25rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 15rpx;
}

.meta-item {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #666;
}

.meta-icon {
  font-size: 28rpx;
  margin-right: 8rpx;
}

.meta-text {
  font-weight: 500;
}

/* 选手对战区域 */
.match-players {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 25rpx;
}

.team-container {
  flex: 1;
  background: #f8f9fa;
  border-radius: 15rpx;
  padding: 20rpx;
  transition: all 0.3s ease;
}

.team-container.winner-team {
  background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
  border: 2rpx solid #ff9800;
}

.team-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.player-list {
  flex: 1;
}

.player-item {
  margin-bottom: 8rpx;
}

.player-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #1a1a1a;
  display: block;
}

.player-ranking {
  font-size: 22rpx;
  color: #666;
  margin-top: 4rpx;
}

.winner-badge {
  margin-left: 15rpx;
}

.winner-icon {
  font-size: 32rpx;
}

.score-display {
  display: flex;
  gap: 8rpx;
  margin-top: 15rpx;
  justify-content: center;
}

.set-score {
  width: 50rpx;
  height: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
  background: white;
  border-radius: 10rpx;
  color: #666;
  border: 2rpx solid #e0e0e0;
}

.set-score.set-winner {
  background: #0A4A39;
  color: white;
  border-color: #0A4A39;
}

.vs-separator {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
}

.vs-text {
  font-size: 24rpx;
  font-weight: bold;
  color: #999;
  background: white;
  padding: 8rpx 12rpx;
  border-radius: 20rpx;
  border: 2rpx solid #e0e0e0;
}

/* 比赛底部区域 */
.match-footer {
  border-top: 1rpx solid #f0f0f0;
  padding-top: 20rpx;
}

.match-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: 20rpx;
  padding: 15rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5rpx;
}

.stat-icon {
  font-size: 28rpx;
}

.stat-label {
  font-size: 20rpx;
  color: #999;
}

.stat-value {
  font-size: 24rpx;
  font-weight: bold;
  color: #333;
}

/* 操作按钮区域 */
.match-actions {
  display: flex;
  gap: 15rpx;
  justify-content: center;
}

.action-btn {
  flex: 1;
  height: 70rpx;
  border-radius: 35rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  font-size: 28rpx;
  font-weight: 600;
  border: none;
  transition: all 0.3s ease;
}

.primary-btn {
  background: linear-gradient(135deg, #0A4A39 0%, #0d5a47 100%);
  color: white;
  box-shadow: 0 4rpx 15rpx rgba(10, 74, 57, 0.3);
}

.primary-btn:active {
  transform: scale(0.95);
}

.live-btn {
  background: linear-gradient(135deg, #0A4A39 0%, #34C759 100%);
  color: white;
  box-shadow: 0 4rpx 15rpx rgba(10, 74, 57, 0.3);
}

.live-btn:active {
  transform: scale(0.95);
}

.secondary-btn {
  background: white;
  color: #666;
  border: 2rpx solid #e0e0e0;
}

.secondary-btn:active {
  background: #f5f5f5;
}

.action-group {
  display: flex;
  gap: 15rpx;
  width: 100%;
}

.action-group .action-btn {
  flex: 1;
}

.btn-icon {
  font-size: 24rpx;
}

.btn-text {
  font-size: 26rpx;
}