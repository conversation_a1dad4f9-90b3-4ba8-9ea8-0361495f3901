<!-- Event Pxml -->
<view class="container">
  <!-- Fixed Tab Bar -->
  <fixed-tab-bar 
    tabs="{{tabs}}"
    activeTab="{{activeTab}}"
    bindtabchange="onTabChange"
    className="event-tab-bar"
  ></fixed-tab-bar>
  
  <!-- 赛事概览 -->
  <view class="events-overview">
    <view class="overview-header">
      <text class="overview-title">🏆 赛事总览</text>
      <text class="overview-count">{{events.length}} 个赛事</text>
    </view>
    <view wx:if="{{events.length > 0}}" class="overview-list">
      <view wx:for="{{events}}" wx:key="_id" class="overview-item">
        <view class="overview-info">
          <text class="overview-name">{{item.name}}</text>
          <text class="overview-type">{{item.eventType}}</text>
        </view>
        <view class="overview-status status-{{item.status}}">
          <text class="status-text">{{item.status === 'ongoing' ? '进行中' : item.status === 'registration' ? '报名中' : item.status === 'upcoming' ? '即将开始' : '已结束'}}</text>
        </view>
      </view>
    </view>
    <view wx:else class="no-events">
      <text class="no-events-text">暂无赛事数据</text>
    </view>
  </view>

  <!-- Tab Content Container -->
  <view
    class="tab-content-container"
    bindtouchstart="onTouchStart"
    bindtouchmove="onTouchMove"
    bindtouchend="onTouchEnd"
  >

    <!-- 页面状态指示器 -->
    <view class="page-status" wx:if="{{loading || error}}">
      <view wx:if="{{loading}}" class="loading-indicator">
        <view class="loading-spinner"></view>
        <text class="loading-text">加载中...</text>
      </view>
      <view wx:if="{{error}}" class="error-indicator">
        <text class="error-icon">⚠️</text>
        <text class="error-text">{{error}}</text>
      </view>
    </view>

    <!-- Tab Content Component -->
    <tab-content 
      activeTab="{{activeTab}}"
      loading="{{loading}}"
      error="{{error}}"
      enableLazyLoad="{{true}}"
      enableCache="{{true}}"
      bindtabcontentchange="onTabContentChange"
      bindlazyload="onTabLazyLoad"
      bindretry="onTabRetry"
    >
      <!-- All Events Tab Content -->
      <view slot="all-events">
        <!-- Search Bar -->
        <view class="search-bar {{showSearch ? 'show' : ''}}">
          <input 
            class="search-input" 
            placeholder="搜索赛事名称、描述、场地..." 
            value="{{searchQuery}}"
            bindinput="onSearchInput"
            bindconfirm="searchEvents"
          />
          <button class="search-submit" bindtap="searchEvents">搜索</button>
        </view>

        <!-- Statistics Panel -->
        <view class="stats-panel" wx:if="{{isLoggedIn && eventStats}}">
          <view class="stats-item">
            <text class="stats-number">{{eventStats.total}}</text>
            <text class="stats-label">总赛事</text>
          </view>
          <view class="stats-item">
            <text class="stats-number">{{eventStats.totalParticipants}}</text>
            <text class="stats-label">总参与者</text>
          </view>
          <view class="stats-item">
            <text class="stats-number">¥{{eventStats.totalRevenue}}</text>
            <text class="stats-label">总收入</text>
          </view>
        </view>

        <!-- User Events Toggle -->
        <view class="user-events-toggle" wx:if="{{isLoggedIn}}">
          <button class="toggle-btn {{showUserEvents ? 'active' : ''}}" bindtap="toggleUserEvents">
            我的赛事
          </button>
          
          <view class="user-event-types" wx:if="{{showUserEvents}}">
            <view 
              class="type-btn {{userEventType === 'all' ? 'active' : ''}}"
              bindtap="switchUserEventType"
              data-type="all"
            >
              全部
            </view>
            <view 
              class="type-btn {{userEventType === 'organized' ? 'active' : ''}}"
              bindtap="switchUserEventType"
              data-type="organized"
            >
              我组织的
            </view>
            <view 
              class="type-btn {{userEventType === 'participated' ? 'active' : ''}}"
              bindtap="switchUserEventType"
              data-type="participated"
            >
              我参与的
            </view>
          </view>
        </view>

        <!-- Filter Panel (Hidden by default) -->
        <view class="filter-panel {{showFilter ? 'show' : ''}}">
          <view class="filter-section">
      <view class="filter-section-title">赛事类型</view>
      <view class="filter-options">
        <view 
          wx:for="{{eventTypes}}" 
          wx:key="id" 
          class="filter-option {{filters.eventType === item.id ? 'active' : ''}}"
          bindtap="applyFilter"
          data-field="eventType"
          data-value="{{item.id}}"
        >
          {{item.name}}
        </view>
      </view>
    </view>

    <view class="filter-section">
      <view class="filter-section-title">赛事状态</view>
      <view class="filter-options">
        <view 
          wx:for="{{statusOptions}}" 
          wx:key="id" 
          class="filter-option {{filters.status === item.id ? 'active' : ''}}"
          bindtap="applyFilter"
          data-field="status"
          data-value="{{item.id}}"
        >
          {{item.name}}
        </view>
      </view>
    </view>
    
    <view class="filter-section">
      <view class="filter-section-title">地区</view>
      <input 
        class="filter-input" 
        placeholder="按地区搜索" 
        value="{{filters.region}}"
        bindinput="inputRegion"
      />
    </view>
    
    <view class="filter-section">
      <view class="filter-section-title">日期范围</view>
      <view class="date-range">
        <picker mode="date" bindchange="startDateChange">
          <view class="date-picker {{filters.dateRange && filters.dateRange.start ? '' : 'placeholder'}}">
            {{filters.dateRange && filters.dateRange.start || '开始日期'}}
          </view>
        </picker>
        <view class="date-separator">至</view>
        <picker mode="date" bindchange="endDateChange">
          <view class="date-picker {{filters.dateRange && filters.dateRange.end ? '' : 'placeholder'}}">
            {{filters.dateRange && filters.dateRange.end || '结束日期'}}
          </view>
        </picker>
      </view>
    </view>

    <view class="filter-section">
      <view class="filter-section-title">排序方式</view>
      <view class="sort-options">
        <view 
          class="sort-option {{sortBy === 'eventDate' ? 'active' : ''}}"
          bindtap="changeSortBy"
          data-sort="eventDate"
        >
          按日期 {{sortBy === 'eventDate' ? (sortOrder === 'asc' ? '↑' : '↓') : ''}}
        </view>
        <view 
          class="sort-option {{sortBy === 'createdAt' ? 'active' : ''}}"
          bindtap="changeSortBy"
          data-sort="createdAt"
        >
          按创建时间 {{sortBy === 'createdAt' ? (sortOrder === 'asc' ? '↑' : '↓') : ''}}
        </view>
        <view 
          class="sort-option {{sortBy === 'currentParticipants' ? 'active' : ''}}"
          bindtap="changeSortBy"
          data-sort="currentParticipants"
        >
          按参与人数 {{sortBy === 'currentParticipants' ? (sortOrder === 'asc' ? '↑' : '↓') : ''}}
        </view>
      </view>
    </view>
    
          <view class="filter-actions">
            <button class="btn-reset" bindtap="resetFilter">重置</button>
            <button class="btn-apply" bindtap="toggleFilter">应用</button>
          </view>
        </view>

        <!-- Batch Actions Bar -->
  <view class="batch-actions-bar {{showBatchActions ? 'show' : ''}}">
    <view class="batch-info">
      已选择 {{selectedEvents.length}} 个赛事
    </view>
    <view class="batch-buttons">
      <button class="batch-btn" bindtap="batchUpdateStatus" data-status="upcoming">
        批量开始
      </button>
      <button class="batch-btn" bindtap="batchUpdateStatus" data-status="cancelled">
        批量取消
      </button>
      <button class="batch-btn clear" bindtap="clearSelection">
        清除选择
      </button>
    </view>
  </view>
  
  <!-- Events List -->
  <view class="events-list {{viewMode}}">
    <!-- List Header with Batch Selection -->
    <view class="list-header" wx:if="{{events.length > 0 && isLoggedIn}}">
      <view class="batch-select">
        <checkbox bindchange="selectAllEvents">全选</checkbox>
      </view>
      <view class="list-count">
        共 {{events.length}} 个赛事
      </view>
    </view>

    <!-- User Events Display -->
    <block wx:if="{{showUserEvents && userEvents.length > 0}}">
      <view class="section-title">我的赛事</view>
      <view
        wx:for="{{userEvents}}"
        wx:key="_id"
        class="event-card user-event {{item.userRole}}"
        bindtap="goToEventDetail"
        data-id="{{item._id}}"
      >
        <!-- Selection Checkbox -->
        <view class="event-select" wx:if="{{isLoggedIn}}" catchtap="toggleEventSelection" data-id="{{item._id}}">
          <checkbox checked="{{selectedEvents.indexOf(item._id) > -1}}"></checkbox>
        </view>

        <!-- Event Card Cover -->
        <view class="event-cover">
          <text class="cover-icon">🎾</text>
          <view class="role-badge {{item.userRole}}">
            {{item.userRole === 'organizer' ? '组织者' : '参与者'}}
          </view>
        </view>
        
        <!-- Event Card Content -->
        <view class="event-content">
          <view class="event-header">
            <view class="event-title">{{item.name}}</view>
            <view class="event-actions">
              <button class="action-btn share" catchtap="shareEvent" data-id="{{item._id}}">
                分享
              </button>
            </view>
          </view>
          
          <view class="event-info">
            <view class="event-type">{{item.eventType}}</view>
            <view class="event-status status-{{item.status}}">
              {{item.status === 'registration' ? '报名中' : 
                item.status === 'upcoming' ? '即将开始' :
                item.status === 'ongoing' ? '进行中' :
                item.status === 'completed' ? '已结束' : '已取消'}}
            </view>
          </view>
          
          <view class="event-details">
            <view class="event-detail">
              <text class="detail-icon">📅</text>
              <text>{{item.eventDate || 'TBD'}}</text>
            </view>
            <view class="event-detail">
              <text class="detail-icon">📍</text>
              <text>{{item.venue || 'TBD'}} • {{item.region || 'TBD'}}</text>
            </view>
            <view class="event-detail">
              <text class="detail-icon">👥</text>
              <text>{{item.currentParticipants || 0}}/{{item.maxParticipants || '∞'}} 人</text>
            </view>
            <view class="event-detail" wx:if="{{item.registrationFee > 0}}">
              <text class="detail-icon">💰</text>
              <text>¥{{item.registrationFee}}</text>
            </view>
          </view>
          
          <view class="event-footer">
            <view class="deadline" wx:if="{{item.registrationDeadline}}">
              报名截止: {{item.registrationDeadline}}
            </view>
            <view class="event-actions">
              <button 
                wx:if="{{item.userRole === 'participant' && item.status === 'registration'}}"
                class="btn-cancel" 
                catchtap="cancelRegistration" 
                data-id="{{item._id}}"
              >
                取消报名
              </button>
              <button 
                wx:elif="{{!item.isRegistered && item.status === 'registration'}}"
                class="btn-register" 
                catchtap="registerEvent" 
                data-id="{{item._id}}"
              >
                报名
              </button>
              <view wx:else class="registration-status">
                {{item.isRegistered ? '已报名' : '不可报名'}}
              </view>
            </view>
          </view>
        </view>
      </view>
    </block>

    <!-- All Events Display -->
    <!-- 调试信息 -->
    <view style="background: #f0f0f0; padding: 10px; margin: 10px; font-size: 12px; border: 2px solid red;">
      调试信息:<br/>
      - activeTab: {{activeTab}}<br/>
      - showUserEvents: {{showUserEvents}}<br/>
      - events.length: {{events.length}}<br/>
      - loading: {{loading}}<br/>
      - error: {{error}}<br/>
      - 渲染条件: {{!showUserEvents && events.length > 0}}
    </view>

    <block wx:if="{{!showUserEvents && events.length > 0}}">
      <view class="section-title" wx:if="{{searchQuery}}">
        搜索结果 "{{searchQuery}}"
      </view>
      <view
        wx:for="{{events}}"
        wx:key="_id"
        class="event-card {{viewMode}}-view"
        bindtap="goToEventDetail"
        data-id="{{item._id}}"
      >
        <!-- Selection Checkbox -->
        <view class="event-select" wx:if="{{isLoggedIn}}" catchtap="toggleEventSelection" data-id="{{item._id}}">
          <checkbox checked="{{selectedEvents.indexOf(item._id) > -1}}"></checkbox>
        </view>

        <!-- Event Card Cover -->
        <view class="event-cover">
          <text class="cover-icon">🎾</text>
          <view class="event-badge" wx:if="{{item.isRegistered}}">
            已报名
          </view>
        </view>
        
        <!-- Event Card Content -->
        <view class="event-content">
          <view class="event-header">
            <view class="event-title">{{item.name}}</view>
            <view class="event-actions">
              <button class="action-btn share" catchtap="shareEvent" data-id="{{item._id}}">
                分享
              </button>
            </view>
          </view>
          
          <view class="event-info">
            <view class="event-type">{{item.eventType}}</view>
            <view class="event-status status-{{item.status}}">
              {{item.status === 'registration' ? '报名中' : 
                item.status === 'upcoming' ? '即将开始' :
                item.status === 'ongoing' ? '进行中' :
                item.status === 'completed' ? '已结束' : '已取消'}}
            </view>
          </view>
          
          <view class="event-details">
            <view class="event-detail">
              <text class="detail-icon">📅</text>
              <text>{{item.eventDate || 'TBD'}}</text>
            </view>
            <view class="event-detail">
              <text class="detail-icon">📍</text>
              <text>{{item.venue || 'TBD'}} • {{item.region || 'TBD'}}</text>
            </view>
            <view class="event-detail">
              <text class="detail-icon">👤</text>
              <text>{{item.organizer.name || 'Tennis Heat'}}</text>
            </view>
            <view class="event-detail">
              <text class="detail-icon">👥</text>
              <text>{{item.currentParticipants || 0}}/{{item.maxParticipants || '∞'}} 人</text>
            </view>
            <view class="event-detail" wx:if="{{item.registrationFee > 0}}">
              <text class="detail-icon">💰</text>
              <text>¥{{item.registrationFee}}</text>
            </view>
          </view>
          
          <view class="event-footer">
            <view class="deadline" wx:if="{{item.registrationDeadline}}">
              报名截止: {{item.registrationDeadline}}
            </view>
            <view class="event-actions">
              <button 
                wx:if="{{item.isRegistered && item.status === 'registration'}}"
                class="btn-cancel" 
                catchtap="cancelRegistration" 
                data-id="{{item._id}}"
              >
                取消报名
              </button>
              <button 
                wx:elif="{{!item.isRegistered && item.status === 'registration'}}"
                class="btn-register" 
                catchtap="registerEvent" 
                data-id="{{item._id}}"
              >
                报名
              </button>
              <button 
                wx:elif="{{item.status === 'registration' && item.registrationFee > 0}}"
                class="btn-pay" 
                catchtap="makePayment" 
                data-id="{{item._id}}"
              >
                支付报名费
              </button>
              <view wx:else class="registration-status">
                {{item.status === 'completed' ? '已结束' : 
                  item.status === 'cancelled' ? '已取消' : '不可报名'}}
              </view>
            </view>
          </view>
        </view>
      </view>
    </block>
    
    <!-- Empty State -->
    <view wx:if="{{!loading && events.length === 0}}" class="empty-state">
      <text class="empty-icon">📭</text>
      <view class="empty-text">暂无赛事数据，点击右上角“创建”按钮发布新赛事！</view>
    </view>
    
    <!-- Loading State -->
    <view wx:if="{{loading}}" class="loading-state">
      <view class="loading-spinner"></view>
      <view class="loading-text">加载赛事中...</view>
    </view>
    
        <!-- Load More -->
        <view wx:if="{{hasMore && !loading && events.length > 0}}" class="load-more" bindtap="loadMoreEvents">
          加载更多
        </view>
        </view>
      </view>
      
      <!-- Search Tab Content -->
      <view slot="search">
        <view class="search-content">
          <view class="search-header">
            <input 
              class="search-input-main" 
              placeholder="搜索赛事..." 
              value="{{tabData.search.query}}"
              bindinput="onSearchTabInput"
              bindconfirm="performSearch"
              focus="{{activeTab === 'search'}}"
            />
            <button class="search-btn-main" bindtap="performSearch">搜索</button>
          </view>
          
          <!-- Search Results -->
          <view wx:if="{{tabData.search.results.length > 0}}" class="search-results">
            <view class="results-header">
              <text>找到 {{tabData.search.results.length}} 个结果</text>
            </view>
            <view
              wx:for="{{tabData.search.results}}"
              wx:key="_id"
              class="event-card search-result"
              bindtap="goToEventDetail"
              data-id="{{item._id}}"
            >
              <!-- Event card content similar to all-events -->
              <view class="event-cover">
                <text class="cover-icon">🎾</text>
              </view>
              <view class="event-content">
                <view class="event-title">{{item.name}}</view>
                <view class="event-info">
                  <view class="event-type">{{item.eventType}}</view>
                  <view class="event-status status-{{item.status}}">
                    {{item.status === 'registration' ? '报名中' : 
                      item.status === 'upcoming' ? '即将开始' :
                      item.status === 'ongoing' ? '进行中' :
                      item.status === 'completed' ? '已结束' : '已取消'}}
                  </view>
                </view>
              </view>
            </view>
          </view>
          
          <!-- Search History -->
          <view wx:elif="{{tabData.search.searchHistory.length > 0}}" class="search-history">
            <view class="history-header">
              <text>搜索历史</text>
              <button class="clear-history" bindtap="clearSearchHistory">清除</button>
            </view>
            <view class="history-items">
              <view 
                wx:for="{{tabData.search.searchHistory}}" 
                wx:key="*this"
                class="history-item"
                bindtap="searchFromHistory"
                data-query="{{item}}"
              >
                {{item}}
              </view>
            </view>
          </view>
          
          <!-- Empty Search State -->
          <view wx:else class="search-empty">
            <text class="empty-icon">🔍</text>
            <view class="empty-text">输入关键词搜索赛事</view>
          </view>
        </view>
      </view>
      
      <!-- Create Tab Content -->
      <view slot="create">
        <view class="create-content">
          <view class="create-header">
            <text class="create-title">创建新赛事</text>
          </view>
          
          <!-- Quick Create Form -->
          <view class="quick-create-form">
            <view class="form-section">
              <view class="form-label">赛事名称</view>
              <input 
                class="form-input" 
                placeholder="请输入赛事名称"
                value="{{tabData.create.formData.name}}"
                bindinput="onCreateFormInput"
                data-field="name"
              />
            </view>
            
            <view class="form-section">
              <view class="form-label">赛事类型</view>
              <picker 
                bindchange="onEventTypeChange" 
                value="{{tabData.create.formData.eventTypeIndex}}" 
                range="{{eventTypes}}" 
                range-key="name"
              >
                <view class="picker-display">
                  {{eventTypes[tabData.create.formData.eventTypeIndex].name || '请选择赛事类型'}}
                </view>
              </picker>
            </view>
            
            <view class="form-section">
              <view class="form-label">比赛场地</view>
              <input 
                class="form-input" 
                placeholder="请输入比赛场地"
                value="{{tabData.create.formData.venue}}"
                bindinput="onCreateFormInput"
                data-field="venue"
              />
            </view>
            
            <view class="form-section">
              <view class="form-label">比赛时间</view>
              <picker 
                mode="multiSelector" 
                bindchange="onDateTimeChange"
                value="{{tabData.create.formData.dateTimeIndex}}"
                range="{{dateTimeRange}}"
              >
                <view class="picker-display">
                  {{tabData.create.formData.eventDate || '请选择比赛时间'}}
                </view>
              </picker>
            </view>
            
            <view class="form-actions">
              <button class="btn-save-draft" bindtap="saveDraft">保存草稿</button>
              <button class="btn-create" bindtap="createEventQuick">立即创建</button>
            </view>
          </view>
          
          <!-- Or Full Form Link -->
          <view class="full-form-link">
            <button class="btn-full-form" bindtap="goToFullCreateForm">
              使用完整表单创建
            </button>
          </view>
        </view>
      </view>
      
      <!-- My Events Tab Content -->
      <view slot="my-events">
        <view class="my-events-content">
          <view class="my-events-header">
            <view class="event-type-tabs">
              <view 
                class="type-tab {{tabData.my.type === 'all' ? 'active' : ''}}"
                bindtap="switchMyEventType"
                data-type="all"
              >
                全部
              </view>
              <view 
                class="type-tab {{tabData.my.type === 'organized' ? 'active' : ''}}"
                bindtap="switchMyEventType"
                data-type="organized"
              >
                我组织的
              </view>
              <view 
                class="type-tab {{tabData.my.type === 'participated' ? 'active' : ''}}"
                bindtap="switchMyEventType"
                data-type="participated"
              >
                我参与的
              </view>
            </view>
          </view>
          
          <!-- My Events List -->
          <view wx:if="{{tabData.my.events.length > 0}}" class="my-events-list">
            <view
              wx:for="{{tabData.my.events}}"
              wx:key="_id"
              class="event-card my-event {{item.userRole}}"
              bindtap="goToEventDetail"
              data-id="{{item._id}}"
            >
              <view class="event-cover">
                <text class="cover-icon">🎾</text>
                <view class="role-badge {{item.userRole}}">
                  {{item.userRole === 'organizer' ? '组织者' : '参与者'}}
                </view>
              </view>
              <view class="event-content">
                <view class="event-title">{{item.name}}</view>
                <view class="event-info">
                  <view class="event-type">{{item.eventType}}</view>
                  <view class="event-status status-{{item.status}}">
                    {{item.status === 'registration' ? '报名中' : 
                      item.status === 'upcoming' ? '即将开始' :
                      item.status === 'ongoing' ? '进行中' :
                      item.status === 'completed' ? '已结束' : '已取消'}}
                  </view>
                </view>
                <view class="quick-actions">
                  <button wx:if="{{item.userRole === 'organizer'}}" class="action-btn edit" catchtap="editEvent" data-id="{{item._id}}">
                    编辑
                  </button>
                  <button class="action-btn share" catchtap="shareEvent" data-id="{{item._id}}">
                    分享
                  </button>
                </view>
              </view>
            </view>
          </view>
          
          <!-- Empty My Events State -->
          <view wx:else class="my-events-empty">
            <text class="empty-icon">📋</text>
            <view class="empty-text">
              {{tabData.my.type === 'organized' ? '您还没有组织过赛事' : 
                tabData.my.type === 'participated' ? '您还没有参与过赛事' : '您还没有相关赛事'}}
            </view>
            <button wx:if="{{tabData.my.type === 'organized'}}" class="btn-create-first" bindtap="switchToCreateTab">
              创建第一个赛事
            </button>
          </view>
        </view>
      </view>
      
      <!-- Popular Tab Content -->
      <view slot="popular">
        <view class="popular-content">
          <view class="popular-header">
            <view class="time-range-tabs">
              <view 
                class="time-tab {{tabData.popular.timeRange === '24h' ? 'active' : ''}}"
                bindtap="switchPopularTimeRange"
                data-range="24h"
              >
                24小时
              </view>
              <view 
                class="time-tab {{tabData.popular.timeRange === '7d' ? 'active' : ''}}"
                bindtap="switchPopularTimeRange"
                data-range="7d"
              >
                7天
              </view>
              <view 
                class="time-tab {{tabData.popular.timeRange === '30d' ? 'active' : ''}}"
                bindtap="switchPopularTimeRange"
                data-range="30d"
              >
                30天
              </view>
            </view>
          </view>
          
          <!-- Popular Events List -->
          <view wx:if="{{tabData.popular.events.length > 0}}" class="popular-events-list">
            <view
              wx:for="{{tabData.popular.events}}"
              wx:key="_id"
              class="event-card popular-event"
              bindtap="goToEventDetail"
              data-id="{{item._id}}"
            >
              <view class="event-cover">
                <text class="cover-icon">🎾</text>
                <view class="popularity-badge">
                  <text class="fire-icon">🔥</text>
                  <text class="popularity-score">{{item.popularityScore || 0}}</text>
                </view>
              </view>
              <view class="event-content">
                <view class="event-title">{{item.name}}</view>
                <view class="event-info">
                  <view class="event-type">{{item.eventType}}</view>
                  <view class="event-status status-{{item.status}}">
                    {{item.status === 'registration' ? '报名中' : 
                      item.status === 'upcoming' ? '即将开始' :
                      item.status === 'ongoing' ? '进行中' :
                      item.status === 'completed' ? '已结束' : '已取消'}}
                  </view>
                </view>
                <view class="popularity-metrics">
                  <view class="metric">
                    <text class="metric-icon">👥</text>
                    <text class="metric-value">{{item.currentParticipants || 0}}</text>
                  </view>
                  <view class="metric">
                    <text class="metric-icon">👀</text>
                    <text class="metric-value">{{item.viewCount || 0}}</text>
                  </view>
                  <view class="metric">
                    <text class="metric-icon">💬</text>
                    <text class="metric-value">{{item.commentCount || 0}}</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
          
          <!-- Empty Popular State -->
          <view wx:else class="popular-empty">
            <text class="empty-icon">🔥</text>
            <view class="empty-text">暂无热门赛事</view>
          </view>
        </view>
      </view>
      
    </tab-content>
    
  </view> <!-- End of tab-content-container -->
</view> 