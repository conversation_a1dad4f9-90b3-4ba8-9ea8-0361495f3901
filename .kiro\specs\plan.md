Here is a comprehensive English AI Development Requirements Document for the tennis social mini-program, strictly based on the provided client documentation and embedded UI references. The prompt is structured for efficient implementation, with phased steps and clear technical specifications. All requirements are derived from 网球社1.Tennis Social Mini-Program AI Development RequirementsDocument Version: 1.1 | Target Language: Chinese UI, English Backend1. Core Functional ModulesA. Flexible Matchmaking & RegistrationRole-Based Access:Players: Join matches, create private events, view stats.Clubs: Create public/private events, manage tournaments, adjust player positions 网球社1.Entry Mechanisms:"Hot Entry" floating buttons on homepage/match lists (UI ref: 网球社1).Substitute player slots and trust-based match approvals 网球社1.B. Tournament ManagementScoring & Structure:Hierarchical data model: Match → Set → Game (e.g., Match: Best-of-3 Sets; Set: 6-4) 网球社1.Tiebreak support (e.g., 7-6) and automatic win-rate calculation.Custom Event Creation:Clubs set: Event type (singles/doubles/mixed), duration, sign-up deadlines, self-score recording toggle 网球社1.Mixed doubles enforce 1:1 gender ratio 网球社1.C. Data Analytics & Social FeaturesPlayer Stats: Manual score entry per set → auto-generate rankings, win rates, and club leaderboards 网球社1.Interactive UI:Cinema-style seat selection for doubles (T1-T16 slots + audience seats; UI ref: 网球社1).Live match streaming and "Star Challenge" events 网球社1.2. User Roles & PermissionsRole	Key PermissionsPlayer	Join matches, create private events, view statsClub	Publish public/private events, manage player positionsAdmin	System oversight (future phase)3. Page Specifications & UI/UXA. Registration/Login PageFields: Unique ID (immutable), nickname, gender, WeChat-synced avatar, 30-character bio, background image (UI ref: 网球社1).Layout: Form-based with WeChat data integration.B. Homepage (Tournament Discovery)Filters: Region (city-level), time range, event type (e.g., men’s singles) 网球社1.Components:Left: Match cards (player avatars, scores, rankings).Right: Fixed "Hot Entry" button + search bar (UI ref: 网球社1).C. Personal CenterPlayer View: Match history, win rates, club leaderboards (clickable).Club View: Event management dashboard + member analytics (UI ref: 网球社1).D. Event Creation FlowInput Steps:Cover image, venue (custom address input), time (default: current hour; e.g., 2025-07-25 11:00).Event type, player self-recording toggle, sign-up deadline 网球社1.Contact info, description (UI ref: 网球社1).4. Data Models & LogicMatch Schema:复制{  "event_type": "Men's Singles", // Enforced options  "stage": "Quarterfinals",      // e.g., 16进8  "duration": "120",            // Minutes  "scores": [{"set": 1, "games": [6,4]}], // Array of sets  "player_recording": true/false // Toggle}Ranking Logic: Auto-calculate based on win/loss ratios and match completion 网球社1.5. UI/UX GuidelinesColor Scheme: Blue/white palette (ref: Roland-Garros UI 网球社1).Responsive Design: Vertical scrolling, fixed action buttons.Icons: Minimalist linear icons (ref: provided UI screenshots).6. Implementation PhasesPhase 1: MVP (2 Weeks)Core: User registration, match creation, basic scoring logic.Pages: Homepage (filters + entry points), personal center skeleton.Phase 2: Data & Social Integration (3 Weeks)Add: Stats dashboards, seat-selection UI for doubles, live streaming.Implement: Self-score recording and mixed doubles gender validation.Phase 3: Optimization & Admin Tools (2 Weeks)Club management console (event moderation, player adjustments).Pagination for large datasets and ranking algorithm refinement.References: All requirements sourced from client docs 网球社1.UI fidelity is critical—refer to embedded screenshots for layout/controls.
