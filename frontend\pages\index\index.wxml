<!-- index.wxml -->
<view class="container">
  <!-- Header -->
  <view class="header">
    <view class="header-content">
      <view class="title-section">
        <text class="app-title">🎾 网球热</text>
        <text class="app-subtitle">专业网球赛事平台</text>
      </view>
      <view class="header-actions">
        <view class="action-btn stats-btn" bindtap="viewMatchStats">
          <text class="action-icon">📊</text>
        </view>
        <view class="action-btn search-btn" bindtap="toggleSearch">
          <text class="action-icon">🔍</text>
        </view>
      </view>
    </view>
  </view>

  <!-- Search Bar -->
  <view class="search-container {{showSearch ? 'show' : ''}}">
    <view class="search-bar">
      <view class="search-input-wrapper">
        <text class="search-prefix-icon">🔍</text>
        <input
          class="search-input"
          placeholder="搜索比赛、选手或场地"
          value="{{searchQuery}}"
          bindinput="onSearchInput"
          bindconfirm="onSearch"
        />
        <view class="search-clear" bindtap="onClearSearch" wx:if="{{searchQuery}}">
          <text class="clear-icon">✕</text>
        </view>
      </view>
      <view class="search-action" bindtap="onSearch">
        <text class="search-action-text">搜索</text>
      </view>
    </view>
  </view>
  
  <!-- Search Results -->
  <view class="search-results" wx:if="{{showSearch && searchQuery}}">
    <view wx:if="{{searchLoading}}" class="search-loading">
      <view class="loading-spinner"></view>
      <text>搜索中...</text>
    </view>
    <view wx:elif="{{searchResults.length > 0}}" class="search-list">
      <view 
        wx:for="{{searchResults}}" 
        wx:key="_id" 
        class="search-item"
        bindtap="goToDetail"
        data-id="{{item._id}}"
      >
        <view class="search-item-title">{{item.matchName || item.eventType}}</view>
        <view class="search-item-info">
          <text class="search-item-venue">{{item.venue}}</text>
          <text class="search-item-time">{{formatMatchTime(item.scheduledTime)}}</text>
        </view>
      </view>
    </view>
    <view wx:elif="{{searchQuery.length >= 2}}" class="search-empty">
      <text>未找到相关比赛</text>
    </view>
  </view>

  <!-- Top Tab Navigation -->
  <view class="tab-nav">
    <view 
      wx:for="{{tabs}}" 
      wx:key="index" 
      class="tab-item {{activeTab === index ? 'active' : ''}}"
      bindtap="switchTab"
      data-index="{{index}}"
    >
      {{item}}
    </view>
  </view>
  
  <!-- Enhanced Filter Bar -->
  <view class="filter-bar">
    <view class="filter-title">比赛筛选</view>
    <view class="filter-btn" bindtap="toggleFilter">
      <text>筛选</text>
      <text class="filter-icon">🔍</text>
    </view>
  </view>
  
  <!-- Filter Panel (Hidden by default) -->
  <view class="filter-panel {{showFilter ? 'show' : ''}}">
    <view class="filter-section">
      <view class="filter-section-title">赛事类型</view>
      <view class="filter-options">
        <view 
          wx:for="{{eventTypes}}" 
          wx:key="id" 
          class="filter-option {{filters.eventType === item.id ? 'active' : ''}}"
          bindtap="applyFilter"
          data-field="eventType"
          data-value="{{item.id}}"
        >
          {{item.name}}
        </view>
      </view>
    </view>
    
    <view class="filter-section">
      <view class="filter-section-title">选手搜索</view>
      <input 
        class="filter-input" 
        placeholder="按选手姓名搜索" 
        value="{{filters.player}}"
        bindinput="inputPlayer"
      />
    </view>
    
    <view class="filter-section">
      <view class="filter-section-title">地区</view>
      <input 
        class="filter-input" 
        placeholder="按地区搜索" 
        value="{{filters.region}}"
        bindinput="inputRegion"
      />
    </view>
    
    <view class="filter-actions">
      <button class="btn-reset" bindtap="resetFilter">重置</button>
      <button class="btn-apply" bindtap="toggleFilter">应用</button>
    </view>
  </view>
  
  <!-- Match List -->
  <view class="match-list">
    <block wx:if="{{matches.length > 0}}">
      <!-- 按日期分组显示比赛 -->
      <view wx:for="{{matches}}" wx:for-item="dateGroup" wx:key="date" class="date-group">
        <!-- 日期标题 -->
        <view class="date-header">
          <text class="date-display">{{dateGroup.dateDisplay}}</text>
        </view>
        
        <!-- 该日期下的比赛列表 -->
        <view wx:for="{{dateGroup.matches}}" wx:for-item="match" wx:key="_id" class="match-card" bindtap="goToDetail" data-id="{{match._id}}">
          <!-- 比赛头部信息 -->
          <view class="match-header">
            <view class="match-title-section">
              <text class="match-name">{{match.matchName || match.eventType}}</text>
              <text class="match-stage">{{match.stage}}</text>
            </view>
            <view class="match-status">
              <view class="status-badge {{match.status === '已结束' ? 'status-completed' : match.status === '比赛中' ? 'status-ongoing' : 'status-registration'}}">
                <view wx:if="{{match.status === '比赛中'}}" class="status-dot-animated"></view>
                <text class="status-text">{{match.status === '已结束' ? '已结束' : match.status === '比赛中' ? '进行中' : '报名中'}}</text>
              </view>
            </view>
          </view>

          <!-- 比赛基本信息 -->
          <view class="match-meta">
            <view class="meta-item">
              <text class="meta-icon">📍</text>
              <text class="meta-text">{{match.venue}}</text>
            </view>
            <view class="meta-item">
              <text class="meta-icon">🕐</text>
              <text class="meta-text">{{formatMatchTime(match.scheduledTime)}}</text>
            </view>
            <view class="meta-item">
              <text class="meta-icon">🌍</text>
              <text class="meta-text">{{match.region}}</text>
            </view>
          </view>
          
          <!-- 选手对战信息 -->
          <view class="players-section">
            <view class="match-players">
              <!-- 队伍1 -->
              <view class="team-container {{match.score && match.score.winner === 'team1' ? 'winner-team' : ''}}">
                <view class="team-info">
                  <view class="player-list">
                    <view wx:for="{{match.players.team1}}" wx:for-item="player" wx:for-index="playerIndex" wx:key="playerIndex" class="player-item">
                      <text class="player-name">{{player.name}}</text>
                      <text class="player-ranking">排名 {{player.ranking}}</text>
                    </view>
                  </view>
                  <view wx:if="{{match.score && match.score.winner === 'team1'}}" class="winner-badge">
                    <text class="winner-icon">🏆</text>
                  </view>
                </view>

                <!-- 队伍1比分 -->
                <view class="score-display" wx:if="{{match.score && match.score.sets.length > 0}}">
                  <view wx:for="{{match.score.sets}}" wx:for-item="set" wx:key="setNumber"
                        class="set-score {{set.team1Score > set.team2Score ? 'set-winner' : ''}}">
                    {{set.team1Score}}
                  </view>
                </view>
              </view>

              <!-- VS 分隔符 -->
              <view class="vs-separator">
                <text class="vs-text">VS</text>
              </view>

              <!-- 队伍2 -->
              <view class="team-container {{match.score && match.score.winner === 'team2' ? 'winner-team' : ''}}">
                <view class="team-info">
                  <view class="player-list">
                    <view wx:for="{{match.players.team2}}" wx:for-item="player" wx:for-index="playerIndex" wx:key="playerIndex" class="player-item">
                      <text class="player-name">{{player.name}}</text>
                      <text class="player-ranking">排名 {{player.ranking}}</text>
                    </view>
                  </view>
                  <view wx:if="{{match.score && match.score.winner === 'team2'}}" class="winner-badge">
                    <text class="winner-icon">🏆</text>
                  </view>
                </view>

                <!-- 队伍2比分 -->
                <view class="score-display" wx:if="{{match.score && match.score.sets.length > 0}}">
                  <view wx:for="{{match.score.sets}}" wx:for-item="set" wx:key="setNumber"
                        class="set-score {{set.team2Score > set.team1Score ? 'set-winner' : ''}}">
                    {{set.team2Score}}
                  </view>
                </view>
              </view>
            </view>
          </view>

          <!-- 比赛统计和操作 -->
          <view class="match-footer">
            <!-- 比赛统计信息 -->
            <view class="match-stats" wx:if="{{match.matchStats}}">
              <view class="stat-item">
                <text class="stat-icon">👥</text>
                <text class="stat-label">观众</text>
                <text class="stat-value">{{match.matchStats.spectatorCount || 0}}</text>
              </view>
              <view class="stat-item">
                <text class="stat-icon">👁</text>
                <text class="stat-label">观看</text>
                <text class="stat-value">{{match.matchStats.viewCount || 0}}</text>
              </view>
              <view class="stat-item" wx:if="{{match.matchStats.duration}}">
                <text class="stat-icon">⏱</text>
                <text class="stat-label">时长</text>
                <text class="stat-value">{{match.matchStats.duration}}</text>
              </view>
            </view>

            <!-- 操作按钮 -->
            <view class="match-actions">
              <button wx:if="{{match.status === '报名中'}}"
                      class="action-btn primary-btn"
                      catchtap="registerMatch"
                      data-id="{{match._id}}">
                <text class="btn-icon">🎾</text>
                <text class="btn-text">立即报名</text>
              </button>
              <view wx:elif="{{match.status === '比赛中'}}" class="action-group">
                <button class="action-btn live-btn"
                        catchtap="joinAsSpectator"
                        data-id="{{match._id}}">
                  <text class="btn-icon">👁</text>
                  <text class="btn-text">观看直播</text>
                </button>
                <button class="action-btn secondary-btn"
                        catchtap="goToDetail"
                        data-id="{{match._id}}">
                  <text class="btn-text">详情</text>
                </button>
              </view>
              <button wx:else
                      class="action-btn secondary-btn"
                      catchtap="goToDetail"
                      data-id="{{match._id}}">
                <text class="btn-text">查看结果</text>
              </button>
            </view>
          </view>
        </view>
      </view>
    </block>
    
    <!-- Skeleton Loader -->
    <block wx:if="{{loading && matches.length === 0}}">
      <view class="skeleton-card">
        <view class="skeleton-header"></view>
        <view class="skeleton-content">
          <view class="skeleton-row">
            <view class="skeleton-avatar"></view>
            <view class="skeleton-text-group">
              <view class="skeleton-text short"></view>
              <view class="skeleton-text long"></view>
            </view>
          </view>
          <view class="skeleton-row">
            <view class="skeleton-avatar"></view>
            <view class="skeleton-text-group">
              <view class="skeleton-text short"></view>
              <view class="skeleton-text long"></view>
            </view>
          </view>
          <view class="skeleton-right-section"></view>
        </view>
      </view>
      <view class="skeleton-card">
        <view class="skeleton-header"></view>
        <view class="skeleton-content">
          <view class="skeleton-row">
            <view class="skeleton-avatar"></view>
            <view class="skeleton-text-group">
              <view class="skeleton-text short"></view>
              <view class="skeleton-text long"></view>
            </view>
          </view>
          <view class="skeleton-row">
            <view class="skeleton-avatar"></view>
            <view class="skeleton-text-group">
              <view class="skeleton-text short"></view>
              <view class="skeleton-text long"></view>
            </view>
          </view>
          <view class="skeleton-right-section"></view>
        </view>
      </view>
    </block>

    <!-- Empty State -->
    <view wx:if="{{!loading && matches.length === 0}}" class="empty-state">
      <text class="empty-icon">📭</text>
      <view class="empty-text">暂无比赛数据，尝试调整筛选条件或稍后再试。</view>
    </view>
    
    <!-- Loading State -->
    <view wx:if="{{loading}}" class="loading-state">
      <view class="loading-spinner"></view>
      <view class="loading-text">加载中...</view>
    </view>
    
    <!-- Load More -->
    <view wx:if="{{hasMore && !loading && matches.length > 0}}" class="load-more" bindtap="loadMoreMatches">
      加载更多
    </view>
  </view>
</view> 