/* event.wxss */

/* 赛事概览样式 */
.events-overview {
  background: white;
  margin: 20rpx 30rpx;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.overview-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #1a1a1a;
}

.overview-count {
  font-size: 24rpx;
  color: #666;
  background: #f8f9fa;
  padding: 8rpx 16rpx;
  border-radius: 15rpx;
}

.overview-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.overview-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 15rpx;
  transition: all 0.3s ease;
}

.overview-item:active {
  transform: scale(0.98);
  background: #f0f0f0;
}

.overview-info {
  flex: 1;
}

.overview-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #1a1a1a;
  margin-bottom: 8rpx;
  display: block;
}

.overview-type {
  font-size: 24rpx;
  color: #666;
}

.overview-status {
  padding: 8rpx 16rpx;
  border-radius: 15rpx;
  font-size: 22rpx;
  font-weight: 600;
}

.status-ongoing {
  background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
  color: #0A4A39;
}

.status-registration {
  background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
  color: #0A4A39;
}

.status-upcoming {
  background: linear-gradient(135deg, #f1f8e9 0%, #dcedc8 100%);
  color: #2e7d32;
}

.status-completed {
  background: linear-gradient(135deg, #f5f5f5 0%, #eeeeee 100%);
  color: #616161;
}

.no-events {
  text-align: center;
  padding: 60rpx 20rpx;
}

.no-events-text {
  font-size: 28rpx;
  color: #999;
}

/* 页面状态指示器 */
.page-status {
  margin: 20rpx 30rpx;
  padding: 30rpx;
  border-radius: 15rpx;
  text-align: center;
}

.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20rpx;
  background: white;
  padding: 40rpx;
  border-radius: 15rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f0f0f0;
  border-top: 4rpx solid #0A4A39;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

.error-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15rpx;
  background: #fff5f5;
  padding: 40rpx;
  border-radius: 15rpx;
  border: 2rpx solid #ffebee;
}

.error-icon {
  font-size: 48rpx;
}

.error-text {
  font-size: 28rpx;
  color: #d32f2f;
  text-align: center;
}

/* Tab Content Container */
.tab-content-container {
  margin-top: 100rpx; /* Account for fixed tab bar */
  min-height: calc(100vh - 100rpx);
  padding: 20rpx;
  box-sizing: border-box;
}

/* Event Tab Bar Specific Styles */
.event-tab-bar {
  background-color: var(--card-background);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* Header (Legacy - kept for backward compatibility) */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 20rpx;
  background-color: var(--card-background);
  margin-bottom: 20rpx;
}

.header-title {
  font-size: 36rpx;
  font-weight: 500;
}

.header-actions {
  display: flex;
}

.filter-btn, .create-btn {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: var(--text-secondary);
  margin-left: 30rpx;
}

.filter-icon, .add-icon {
  font-size: 32rpx;
  margin-right: 8rpx;
}

/* Filter Panel */
.filter-panel {
  position: fixed;
  top: 0;
  right: -100%;
  width: 70%;
  height: 100%;
  background-color: var(--card-background);
  z-index: 1000;
  box-shadow: -4rpx 0 10rpx rgba(0, 0, 0, 0.1);
  transition: right 0.3s ease;
  padding: 30rpx 20rpx;
  box-sizing: border-box;
}

.filter-panel.show {
  right: 0;
}

.filter-section {
  margin-bottom: 30rpx;
}

.filter-section-title {
  font-size: 28rpx;
  font-weight: 500;
  margin-bottom: 16rpx;
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
}

.filter-option {
  padding: 12rpx 20rpx;
  border: 1rpx solid var(--border-color);
  border-radius: 4rpx;
  margin-right: 16rpx;
  margin-bottom: 16rpx;
  font-size: 24rpx;
}

.filter-option.active {
  background-color: var(--primary-color);
  color: var(--text-on-primary);
  border-color: var(--primary-color);
}

.filter-input {
  width: 100%;
  height: 70rpx;
  border: 1rpx solid var(--border-color);
  border-radius: 4rpx;
  padding: 0 16rpx;
  box-sizing: border-box;
  font-size: 26rpx;
}

.date-range {
  display: flex;
  align-items: center;
}

.date-picker {
  flex: 1;
  height: 70rpx;
  line-height: 70rpx;
  border: 1rpx solid var(--border-color);
  border-radius: 4rpx;
  padding: 0 16rpx;
  box-sizing: border-box;
  font-size: 26rpx;
}

.date-picker.placeholder {
  color: var(--text-tertiary);
}

.date-separator {
  padding: 0 16rpx;
  font-size: 26rpx;
  color: var(--text-secondary);
}

.filter-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 40rpx;
}

.btn-reset, .btn-apply {
  width: 45%;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 4rpx;
  font-size: 28rpx;
}

.btn-reset {
  background-color: #f5f5f5;
  color: var(--text-secondary);
}

.btn-apply {
  background-color: var(--primary-color);
  color: var(--text-on-primary);
}

/* Events List */
.events-list {
  padding: 20rpx;
}

.event-card {
  background-color: var(--card-background);
  border-radius: 8rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.event-cover {
  font-size: 300rpx;
  text-align: center;
  display: block;
  margin-bottom: 20rpx;
}

.event-content {
  padding: 20rpx;
}

.event-title {
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: 16rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.event-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.event-type {
  font-size: 26rpx;
  color: var(--text-secondary);
}

.event-status {
  font-size: 24rpx;
  padding: 6rpx 12rpx;
  border-radius: 4rpx;
  text-transform: uppercase;
}

.status-completed {
  background-color: var(--primary-color);
  color: var(--text-on-primary);
}

.status-ongoing {
  background-color: #FF9500;
  color: #FFFFFF;
}

.status-registration {
  background-color: #34C759;
  color: #FFFFFF;
}

.event-details {
  margin-bottom: 20rpx;
}

.event-detail {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
  font-size: 26rpx;
  color: var(--text-secondary);
}

.detail-icon {
  font-size: 28rpx;
  margin-right: 10rpx;
}

.event-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid var(--border-color);
}

.deadline {
  font-size: 24rpx;
  color: #FF3B30;
}

.btn-register {
  background-color: var(--primary-color);
  color: var(--text-on-primary);
  font-size: 26rpx;
  padding: 12rpx 30rpx;
  border-radius: 4rpx;
}

/* Empty State */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-icon {
  font-size: 160rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: var(--text-tertiary);
}

/* Loading State */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(0, 0, 0, 0.1);
  border-top-color: var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16rpx;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loading-text {
  font-size: 26rpx;
  color: var(--text-tertiary);
}

/* Load More */
.load-more {
  text-align: center;
  padding: 20rpx 0;
  font-size: 26rpx;
  color: var(--text-secondary);
} 